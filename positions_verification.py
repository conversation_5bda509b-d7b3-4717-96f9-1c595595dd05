from datetime import datetime
import os
from config import logger
from dotenv import load_dotenv
import json
from Generic_Squareoff import squareoff_positions
from Fyers import Fyers_save_final_pnl
import sys

load_dotenv()

def generic_position_verification(broker, positions, context=""):
    """
    Simplified position verification function to validate BCS strategy positions.
    
    Args:
        broker: Broker context object
        positions: Current positions dictionary from get_positions()
        context: String to identify where this verification is called from
    
    Returns:
        bool: True if verification passes, False if verification fails
    """
    try:
        logger.info(f"Starting position verification - Context: {context}")
        
        # Check if today is expiry day
        expiry_day = os.getenv("Expiry_Day", "THURSDAY").upper()
        today_day = datetime.now().strftime("%A").upper()
        is_expiry_day = (today_day == expiry_day)
        
        logger.info(f"Today: {today_day}, Expiry Day: {expiry_day}, Is Expiry Day: {is_expiry_day}")
        
        # Count active positions and collect quantities
        active_positions_count = 0
        quantities = []
        
        for position_type, position_data in positions.items():
            if position_type in ["CE_BUY", "CE_SHORT", "PE_BUY", "PE_SHORT"]:
                if isinstance(position_data, list):
                    # For expiry day when CE_BUY and PE_BUY are lists
                    for pos in position_data:
                        if pos.get("qty", 0) > 0:
                            active_positions_count += 1
                            quantities.append(abs(pos.get("qty", 0)))
                elif isinstance(position_data, dict) and position_data.get("qty", 0) > 0:
                    # For normal day or SHORT positions
                    active_positions_count += 1
                    quantities.append(abs(position_data.get("qty", 0)))
        
        logger.info(f"Total active positions found: {active_positions_count}")
        logger.info(f"Quantities found: {quantities}")
        
        # Check 1: Position Count
        expected_count = 6 if is_expiry_day else 4
        if active_positions_count != expected_count:
            logger.error(f"Position count FAILED. Expected: {expected_count}, Found: {active_positions_count}")
            return False
        
        logger.info(f"Position count PASSED. Found {active_positions_count} positions as expected.")
        
        # Check 2: Quantity Consistency
        if len(quantities) == 0:
            logger.error("No quantities found")
            return False
        
        if len(set(quantities)) != 1:
            logger.error(f"Quantity consistency FAILED. Different quantities: {quantities}")
            return False
        
        actual_qty = quantities[0]
        logger.info(f"All positions have same quantity: {actual_qty}")
        
        # Check 3: Expected Quantity
        global_qty = int(os.getenv("GLOBAL_QTY", "0"))
        if global_qty == 0:
            logger.warning("GLOBAL_QTY not set or is 0")
            return True  # Skip quantity check if not configured
        
        if is_expiry_day:
            expected_qty = global_qty // 2  # Half quantity on expiry day
        else:
            expected_qty = global_qty  # Full quantity on normal days
        
        if actual_qty != expected_qty:
            logger.error(f"Quantity check FAILED. Expected: {expected_qty}, Found: {actual_qty}")
            return False
        
        logger.info(f"Quantity check PASSED. Expected: {expected_qty}, Found: {actual_qty}")
        logger.info(f"Position verification COMPLETED SUCCESSFULLY - Context: {context}")
        return True
        
    except Exception as e:
        logger.error(f"Position verification ERROR - Context: {context}, Error: {e}")
        return False


def exit_trading(broker, all_positions, now, start_time, exit_time):
    squareoff_positions(broker, all_positions)  # Square off everything
    logger.info(f"Squared off all positions for today {now}. Time to save PNL and exit...")  
    Fyers_save_final_pnl.save_final_pnl(broker, start_time, exit_time) 
    logger.info(f"Today's {now}, PNL is saved. Exiting the script.")
    sys.exit()

